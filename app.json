{"expo": {"scheme": "routertv", "plugins": [["@react-native-tvos/config-tv", {"androidTVBanner": "./assets/tv_icons/icon-400x240.png", "appleTVImages": {"icon": "./assets/tv_icons/icon-1280x768.png", "iconSmall": "./assets/tv_icons/icon-400x240.png", "iconSmall2x": "./assets/tv_icons/icon-800x480.png", "topShelf": "./assets/tv_icons/icon-1920x720.png", "topShelf2x": "./assets/tv_icons/icon-3840x1440.png", "topShelfWide": "./assets/tv_icons/icon-2320x720.png", "topShelfWide2x": "./assets/tv_icons/icon-4640x1440.png"}}], "expo-router", "expo-font"], "android": {"edgeToEdgeEnabled": true, "splash": {"image": "./assets/images/splash.png"}, "package": "com.dark_code.histreamstv"}, "ios": {"splash": {"image": "./assets/images/splash.png"}, "bundleIdentifier": "com.dark-code.histreamstv"}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png", "output": "static"}, "experiments": {"typedRoutes": true}, "newArchEnabled": true, "name": "histreams-tv", "slug": "histreams-tv"}}