{"name": "histreams-tv", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "EXPO_TV=1 expo start", "android": "EXPO_TV=1 expo run:android", "ios": "EXPO_TV=1 expo run:ios", "web": "expo start --web", "reset-project": "./scripts/reset-project.js", "lint": "expo lint", "prebuild": "EXPO_TV=1 expo prebuild --clean", "deploy": "npx expo export -p web && npx eas-cli@latest deploy"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "expo": "^53.0.16", "expo-build-properties": "~0.14.5", "expo-constants": "~17.1.3", "expo-font": "~13.3.0", "expo-linking": "~7.1.3", "expo-router": "~5.1.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "npm:react-native-tvos@0.79.5-0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-tvos/config-tv": "^0.1.1", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "expo": {"install": {"exclude": ["react-native"]}}, "private": true}